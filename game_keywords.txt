1. Puzzle (益智游戏)
2. Clicker (点击游戏)
3. RPG (角色扮演游戏)
4. Action (动作游戏)
5. Adventure (冒险游戏)
6. Strategy (策略游戏)
7. Simulation (模拟游戏)
8. Racing (赛车游戏)
9. Sports (体育游戏)
10. Fighting (格斗游戏)
11. Shooter (射击游戏)
12. Platform (平台游戏)
13. Arcade (街机游戏)
14. Casual (休闲游戏)
15. Indie (独立游戏)
16. MMO (大型多人在线游戏)
17. MMORPG (大型多人在线角色扮演游戏)
18. MOBA (多人在线战术竞技游戏)
19. Battle Royale (大逃杀游戏)
20. Tower Defense (塔防游戏)
21. Card Game (卡牌游戏)
22. Board Game (棋盘游戏)
23. Match-3 (三消游戏)
24. Endless Runner (无尽跑酷游戏)
25. Idle Game (放置游戏)
26. Roguelike (类Rogue游戏)
27. Sandbox (沙盒游戏)
28. Survival (生存游戏)
29. Horror (恐怖游戏)
30. Stealth (潜行游戏)
31. Open World (开放世界游戏)
32. Multiplayer (多人游戏)
33. Single Player (单人游戏)
34. Co-op (合作游戏)
35. PvP (玩家对战)
36. PvE (玩家对环境)
37. Turn-based (回合制游戏)
38. Real-time (实时游戏)
39. 2D Game (2D游戏)
40. 3D Game (3D游戏)
41. Pixel Art (像素艺术游戏)
42. Retro Game (复古游戏)
43. Mobile Game (手机游戏)
44. Console Game (主机游戏)
45. PC Game (电脑游戏)
46. VR Game (虚拟现实游戏)
47. AR Game (增强现实游戏)
48. Free-to-Play (免费游戏)
49. Premium Game (付费游戏)
50. Freemium (免费增值游戏)
51. Gacha (抽卡游戏)
52. Loot Box (战利品箱)
53. Microtransaction (微交易)
54. DLC (可下载内容)
55. Season Pass (季票)
56. Beta Test (测试版)
57. Alpha Test (内测版)
58. Early Access (抢先体验)
59. Game Engine (游戏引擎)
60. Unity (Unity引擎)
61. Unreal Engine (虚幻引擎)
62. Godot (Godot引擎)
63. GameMaker (GameMaker引擎)
64. Construct (Construct引擎)
65. RPG Maker (RPG制作大师)
66. Level Design (关卡设计)
67. Game Design (游戏设计)
68. Character Design (角色设计)
69. UI Design (界面设计)
70. Sound Design (音效设计)
71. Music Composer (音乐作曲)
72. Game Art (游戏美术)
73. Animation (动画)
74. Rigging (绑定)
75. Texturing (贴图)
76. Modeling (建模)
77. Lighting (光照)
78. Shading (着色)
79. Particle Effects (粒子效果)
80. Visual Effects (视觉效果)
81. Game Programming (游戏编程)
82. C# Programming (C#编程)
83. C++ Programming (C++编程)
84. JavaScript (JavaScript)
85. Python (Python)
86. Lua Scripting (Lua脚本)
87. Blueprint (蓝图)
88. Visual Scripting (可视化脚本)
89. AI Programming (AI编程)
90. Physics Engine (物理引擎)
91. Collision Detection (碰撞检测)
92. Pathfinding (寻路)
93. State Machine (状态机)
94. Event System (事件系统)
95. Save System (存档系统)
96. Inventory System (背包系统)
97. Combat System (战斗系统)
98. Dialogue System (对话系统)
99. Quest System (任务系统)
100. Achievement System (成就系统)
101. Leaderboard (排行榜)
102. Analytics (数据分析)
103. Monetization (变现)
104. User Acquisition (用户获取)
105. Retention (留存)
106. Engagement (参与度)
107. Conversion Rate (转化率)
108. ARPU (平均每用户收入)
109. LTV (用户生命周期价值)
110. DAU (日活跃用户)
111. MAU (月活跃用户)
112. Churn Rate (流失率)
113. Game Balance (游戏平衡)
114. Difficulty Curve (难度曲线)
115. Player Progression (玩家进度)
116. Skill Tree (技能树)
117. Character Class (角色职业)
118. Weapon System (武器系统)
119. Magic System (魔法系统)
120. Crafting System (制作系统)
121. Trading System (交易系统)
122. Guild System (公会系统)
123. Clan System (战队系统)
124. Tournament (锦标赛)
125. Championship (冠军赛)
126. Esports (电子竞技)
127. Streaming (直播)
128. Twitch (Twitch平台)
129. YouTube Gaming (YouTube游戏)
130. Game Review (游戏评测)
131. Game Trailer (游戏预告片)
132. Gameplay Video (游戏实况视频)
133. Walkthrough (游戏攻略)
134. Speedrun (速通)
135. Let's Play (实况解说)
136. Game Mod (游戏模组)
137. Modding (模组制作)
138. Community (社区)
139. Forum (论坛)
140. Discord (Discord)
141. Reddit (Reddit)
142. Steam (Steam平台)
143. Epic Games Store (Epic游戏商店)
144. GOG (GOG平台)
145. Itch.io (Itch.io平台)
146. App Store (应用商店)
147. Google Play (Google Play商店)
148. Nintendo eShop (任天堂商店)
149. PlayStation Store (PlayStation商店)
150. Xbox Store (Xbox商店)
151. Game Publisher (游戏发行商)
152. Game Developer (游戏开发商)
153. Indie Developer (独立开发者)
154. AAA Game (3A游戏)
155. AA Game (2A游戏)
156. Kickstarter (众筹)
157. Game Jam (游戏开发竞赛)
158. Prototype (原型)
159. Vertical Slice (垂直切片)
160. Minimum Viable Product (最小可行产品)
161. Game Loop (游戏循环)
162. Core Gameplay (核心玩法)
163. Meta Game (元游戏)
164. Game Mechanics (游戏机制)
165. Game Rules (游戏规则)
166. Win Condition (胜利条件)
167. Lose Condition (失败条件)
168. Score System (计分系统)
169. Combo System (连击系统)
170. Bonus System (奖励系统)
171. Power-up (道具增强)
172. Collectible (收集品)
173. Hidden Object (隐藏物品)
174. Easter Egg (彩蛋)
175. Secret Level (隐藏关卡)
176. Boss Fight (Boss战)
177. Mini Boss (小Boss)
178. Final Boss (最终Boss)
179. Checkpoint (检查点)
180. Respawn (重生)
181. Game Over (游戏结束)
182. Continue (继续)
183. Pause Menu (暂停菜单)
184. Settings Menu (设置菜单)
185. Options Menu (选项菜单)
186. Main Menu (主菜单)
187. Loading Screen (加载界面)
188. Splash Screen (启动画面)
189. Tutorial (教程)
190. Onboarding (新手引导)
191. Help System (帮助系统)
192. Hint System (提示系统)
193. Skip Function (跳过功能)
194. Auto-save (自动保存)
195. Manual Save (手动保存)
196. Cloud Save (云存档)
197. Cross-platform (跨平台)
198. Cross-play (跨平台游戏)
199. Cross-progression (跨平台进度)
200. Localization (本地化)
201. Translation (翻译)
202. Voice Acting (配音)
203. Subtitles (字幕)
204. Accessibility (无障碍)
205. Colorblind Support (色盲支持)
206. Controller Support (手柄支持)
207. Keyboard Support (键盘支持)
208. Mouse Support (鼠标支持)
209. Touch Controls (触控操作)
210. Gesture Controls (手势控制)
211. Motion Controls (体感控制)
212. Haptic Feedback (触觉反馈)
213. Rumble (震动)
214. Audio Cue (音频提示)
215. Visual Cue (视觉提示)
216. Feedback System (反馈系统)
217. Notification System (通知系统)
218. Push Notification (推送通知)
219. In-app Purchase (应用内购买)
220. Virtual Currency (虚拟货币)
221. Premium Currency (高级货币)
222. Soft Currency (软货币)
223. Hard Currency (硬货币)
224. Energy System (体力系统)
225. Stamina System (耐力系统)
226. Cooldown (冷却时间)
227. Daily Reward (每日奖励)
228. Weekly Challenge (每周挑战)
229. Monthly Event (月度活动)
230. Seasonal Event (季节活动)
231. Limited Time Event (限时活动)
232. Special Offer (特别优惠)
233. Bundle Deal (捆绑优惠)
234. Discount (折扣)
235. Sale (促销)
236. Black Friday (黑色星期五)
237. Holiday Sale (节日促销)
238. Summer Sale (夏季促销)
239. Winter Sale (冬季促销)
240. Launch Sale (首发促销)
241. Pre-order (预购)
242. Wishlist (愿望清单)
243. Game Library (游戏库)
244. Collection (收藏)
245. Favorites (收藏夹)
246. Recently Played (最近游玩)
247. Play Time (游戏时长)
248. Game Session (游戏会话)
249. Multiplayer Lobby (多人大厅)
250. Matchmaking (匹配系统)
251. Server Browser (服务器浏览器)
252. Dedicated Server (专用服务器)
253. Peer-to-Peer (点对点)
254. Lag (延迟)
255. Ping (网络延迟)
256. Latency (延迟)
257. Bandwidth (带宽)
258. Connection (连接)
259. Offline Mode (离线模式)
260. Online Mode (在线模式)
261. Network Error (网络错误)
262. Server Maintenance (服务器维护)
263. Update (更新)
264. Patch (补丁)
265. Hotfix (热修复)
266. Bug Fix (错误修复)
267. Balance Update (平衡更新)
268. Content Update (内容更新)
269. Feature Update (功能更新)
270. Security Update (安全更新)
271. Performance Optimization (性能优化)
272. Frame Rate (帧率)
273. FPS (每秒帧数)
274. Resolution (分辨率)
275. Graphics Quality (画质)
276. Texture Quality (贴图质量)
277. Shadow Quality (阴影质量)
278. Anti-aliasing (抗锯齿)
279. V-Sync (垂直同步)
280. HDR (高动态范围)
281. Ray Tracing (光线追踪)
282. DLSS (深度学习超级采样)
283. FSR (FidelityFX超级分辨率)
284. Benchmark (基准测试)
285. System Requirements (系统要求)
286. Minimum Requirements (最低配置)
287. Recommended Requirements (推荐配置)
288. Hardware Compatibility (硬件兼容性)
289. Driver Update (驱动更新)
290. Game Crash (游戏崩溃)
291. Error Message (错误信息)
292. Troubleshooting (故障排除)
293. Technical Support (技术支持)
294. Customer Service (客户服务)
295. Bug Report (错误报告)
296. Feedback (反馈)
297. Suggestion (建议)
298. Feature Request (功能请求)
299. User Experience (用户体验)
300. User Interface (用户界面)
301. Menu Design (菜单设计)
302. HUD (抬头显示)
303. Minimap (小地图)
304. Health Bar (血条)
305. Mana Bar (魔法条)
306. Experience Bar (经验条)
307. Progress Bar (进度条)
308. Button (按钮)
309. Icon (图标)
310. Cursor (光标)
311. Tooltip (工具提示)
312. Pop-up (弹窗)
313. Modal Dialog (模态对话框)
314. Confirmation Dialog (确认对话框)
315. Alert Dialog (警告对话框)
316. Loading Bar (加载条)
317. Spinner (加载动画)
318. Transition (过渡效果)
319. Animation Curve (动画曲线)
320. Tween (补间动画)
321. Particle System (粒子系统)
322. Shader (着色器)
323. Material (材质)
324. Sprite (精灵)
325. Tilemap (瓦片地图)
326. Tileset (瓦片集)
327. Atlas (图集)
328. Spritesheet (精灵表)
329. Animation Frame (动画帧)
330. Keyframe (关键帧)
331. Bone Animation (骨骼动画)
332. Skeletal Animation (骨架动画)
333. Morph Target (变形目标)
334. Blend Shape (混合形状)
335. Inverse Kinematics (反向运动学)
336. Forward Kinematics (正向运动学)
337. Motion Capture (动作捕捉)
338. Procedural Animation (程序化动画)
339. Physics Simulation (物理模拟)
340. Rigid Body (刚体)
341. Soft Body (软体)
342. Fluid Simulation (流体模拟)
343. Cloth Simulation (布料模拟)
344. Hair Simulation (毛发模拟)
345. Destruction (破坏效果)
346. Explosion (爆炸效果)
347. Fire Effect (火焰效果)
348. Water Effect (水效果)
349. Smoke Effect (烟雾效果)
350. Magic Effect (魔法效果)
351. Lightning Effect (闪电效果)
352. Glow Effect (发光效果)
353. Bloom Effect (泛光效果)
354. Lens Flare (镜头光晕)
355. Screen Space Reflection (屏幕空间反射)
356. Ambient Occlusion (环境光遮蔽)
357. Global Illumination (全局光照)
358. Volumetric Lighting (体积光)
359. Fog (雾效)
360. Depth of Field (景深)
361. Motion Blur (运动模糊)
362. Film Grain (胶片颗粒)
363. Vignette (暗角)
364. Color Grading (调色)
365. Post Processing (后处理)
366. Render Pipeline (渲染管线)
367. Forward Rendering (前向渲染)
368. Deferred Rendering (延迟渲染)
369. Culling (剔除)
370. Occlusion Culling (遮挡剔除)
371. Frustum Culling (视锥剔除)
372. Level of Detail (细节层次)
373. Mesh Optimization (网格优化)
374. Texture Compression (纹理压缩)
375. Mipmapping (多级纹理)
376. Texture Streaming (纹理流)
377. Asset Bundle (资源包)
378. Resource Management (资源管理)
379. Memory Management (内存管理)
380. Garbage Collection (垃圾回收)
381. Object Pooling (对象池)
382. Profiling (性能分析)
383. Debugging (调试)
384. Console Commands (控制台命令)
385. Cheat Codes (作弊码)
386. Developer Mode (开发者模式)
387. Debug Menu (调试菜单)
388. Wireframe Mode (线框模式)
389. Collision Visualization (碰撞可视化)
390. Performance Metrics (性能指标)
391. Memory Usage (内存使用)
392. CPU Usage (CPU使用率)
393. GPU Usage (GPU使用率)
394. Draw Calls (绘制调用)
395. Batching (批处理)
396. Instancing (实例化)
397. Multithreading (多线程)
398. Coroutine (协程)
399. Async Loading (异步加载)
400. Streaming (流式加载)
401. Preloading (预加载)
402. Caching (缓存)
403. Compression (压缩)
404. Encryption (加密)
405. Anti-cheat (反作弊)
406. Security (安全)
407. Privacy (隐私)
408. GDPR Compliance (GDPR合规)
409. Age Rating (年龄分级)
410. Content Warning (内容警告)
411. Parental Controls (家长控制)
412. Safe Chat (安全聊天)
413. Moderation (内容审核)
414. Report System (举报系统)
415. Ban System (封禁系统)
416. Mute System (禁言系统)
417. Kick System (踢出系统)
418. Admin Tools (管理工具)
419. Server Commands (服务器命令)
420. Game Master (游戏管理员)
421. Community Manager (社区管理员)
422. Player Support (玩家支持)
423. Live Chat (在线聊天)
424. Help Desk (帮助台)
425. FAQ (常见问题)
426. Knowledge Base (知识库)
427. Documentation (文档)
428. API Documentation (API文档)
429. SDK (软件开发工具包)
430. Plugin API (插件API)
431. Scripting API (脚本API)
432. Modding API (模组API)
433. Workshop (创意工坊)
434. User Generated Content (用户生成内容)
435. Level Editor (关卡编辑器)
436. Map Editor (地图编辑器)
437. Character Creator (角色创建器)
438. Skin Editor (皮肤编辑器)
439. Texture Editor (纹理编辑器)
440. Animation Editor (动画编辑器)
441. Script Editor (脚本编辑器)
442. Dialogue Editor (对话编辑器)
443. Quest Editor (任务编辑器)
444. Item Editor (物品编辑器)
445. Skill Editor (技能编辑器)
446. Spell Editor (法术编辑器)
447. AI Editor (AI编辑器)
448. Behavior Tree (行为树)
449. State Machine Editor (状态机编辑器)
450. Node Editor (节点编辑器)
451. Graph Editor (图形编辑器)
452. Timeline Editor (时间轴编辑器)
453. Curve Editor (曲线编辑器)
454. Gradient Editor (渐变编辑器)
455. Color Picker (颜色选择器)
456. Asset Browser (资源浏览器)
457. Project Browser (项目浏览器)
458. Scene Hierarchy (场景层级)
459. Inspector Panel (检视面板)
460. Properties Panel (属性面板)
461. Console Window (控制台窗口)
462. Output Window (输出窗口)
463. Error Log (错误日志)
464. Warning Log (警告日志)
465. Build Settings (构建设置)
466. Player Settings (播放器设置)
467. Quality Settings (质量设置)
468. Input Settings (输入设置)
469. Audio Settings (音频设置)
470. Physics Settings (物理设置)
471. Lighting Settings (光照设置)
472. Rendering Settings (渲染设置)
473. Platform Settings (平台设置)
474. Export Settings (导出设置)
475. Packaging (打包)
476. Distribution (分发)
477. Publishing (发布)
478. Marketing (营销)
479. Promotion (推广)
480. Social Media (社交媒体)
481. Influencer Marketing (网红营销)
482. Press Release (新闻稿)
483. Game Awards (游戏奖项)
484. Game Festival (游戏节)
485. Game Conference (游戏会议)
486. Game Exhibition (游戏展览)
487. Demo Version (演示版)
488. Playtest (游戏测试)
489. Focus Group (焦点小组)
490. User Research (用户研究)
491. A/B Testing (A/B测试)
492. Metrics (指标)
493. KPI (关键绩效指标)
494. ROI (投资回报率)
495. Market Research (市场研究)
496. Competitor Analysis (竞品分析)
497. Target Audience (目标受众)
498. Player Persona (玩家画像)
499. Game Pitch (游戏提案)
500. Game Portfolio (游戏作品集)
